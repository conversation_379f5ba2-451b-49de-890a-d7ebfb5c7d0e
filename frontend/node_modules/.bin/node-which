#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/project/llm/motion-agent/frontend/node_modules/which/bin/node_modules:/Users/<USER>/project/llm/motion-agent/frontend/node_modules/which/node_modules:/Users/<USER>/project/llm/motion-agent/frontend/node_modules:/Users/<USER>/project/llm/motion-agent/node_modules:/Users/<USER>/project/llm/node_modules:/Users/<USER>/project/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/project/llm/motion-agent/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/project/llm/motion-agent/frontend/node_modules/which/bin/node_modules:/Users/<USER>/project/llm/motion-agent/frontend/node_modules/which/node_modules:/Users/<USER>/project/llm/motion-agent/frontend/node_modules:/Users/<USER>/project/llm/motion-agent/node_modules:/Users/<USER>/project/llm/node_modules:/Users/<USER>/project/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/project/llm/motion-agent/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../which/bin/node-which" "$@"
else
  exec node  "$basedir/../which/bin/node-which" "$@"
fi
