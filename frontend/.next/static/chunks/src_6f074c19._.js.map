{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/llm/motion-agent/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { \n  TextInput, \n  AdvancedTextInput, \n  AnimationRequest, \n  MotionResponse, \n  AnimationResponse, \n  AnimationPresets, \n  ExampleRequest,\n  HealthStatus \n} from '@/types/motion';\n\n// API Base URL - Backend runs on port 9001\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000, // 30 seconds timeout for animation generation\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// API Functions\n\n/**\n * Health check endpoint\n */\nexport const checkHealth = async (): Promise<HealthStatus> => {\n  const response = await api.get('/health');\n  return response.data;\n};\n\n/**\n * Basic motion generation\n */\nexport const generateMotion = async (input: TextInput): Promise<MotionResponse> => {\n  const response = await api.post('/generate-motion', input);\n  return response.data;\n};\n\n/**\n * Advanced motion generation with LangGraph\n */\nexport const generateMotionAdvanced = async (input: AdvancedTextInput): Promise<MotionResponse> => {\n  const response = await api.post('/generate-motion-advanced', input);\n  return response.data;\n};\n\n/**\n * Professional animation generation\n */\nexport const generateProfessionalAnimation = async (request: AnimationRequest): Promise<AnimationResponse> => {\n  const response = await api.post('/animation/generate', request);\n  return response.data;\n};\n\n/**\n * Get animation presets\n */\nexport const getAnimationPresets = async (): Promise<AnimationPresets> => {\n  const response = await api.get('/animation/presets');\n  return response.data;\n};\n\n/**\n * Get example requests\n */\nexport const getExampleRequests = async (): Promise<{\n  simple_examples: ExampleRequest[];\n  intermediate_examples: ExampleRequest[];\n  advanced_examples: ExampleRequest[];\n}> => {\n  const response = await api.get('/animation/examples');\n  return response.data;\n};\n\n/**\n * Animation system health check\n */\nexport const checkAnimationHealth = async () => {\n  const response = await api.get('/animation/health');\n  return response.data;\n};\n\n// Error handling wrapper\nexport const handleApiError = (error: any): string => {\n  if (error.response) {\n    // Server responded with error status\n    return error.response.data?.detail || error.response.data?.message || 'Server error occurred';\n  } else if (error.request) {\n    // Request was made but no response received\n    return 'Unable to connect to the server. Please check if the backend is running.';\n  } else {\n    // Something else happened\n    return error.message || 'An unexpected error occurred';\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAaqB;AAbrB;;AAYA,2CAA2C;AAC3C,MAAM,eAAe,+RAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,4CAA4C;AAC5C,MAAM,MAAM,0LAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAOO,MAAM,cAAc;IACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;IAC/B,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;IACpD,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;IAC7D,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;IACvD,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;IAC/B,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,qBAAqB;IAKhC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;IAC/B,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,uBAAuB;IAClC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;IAC/B,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,MAAM,QAAQ,EAAE;QAClB,qCAAqC;QACrC,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,UAAU,MAAM,QAAQ,CAAC,IAAI,EAAE,WAAW;IACxE,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,4CAA4C;QAC5C,OAAO;IACT,OAAO;QACL,0BAA0B;QAC1B,OAAO,MAAM,OAAO,IAAI;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/llm/motion-agent/frontend/src/components/MotionGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  generateMotion, \n  generateMotionAdvanced, \n  generateProfessionalAnimation,\n  getAnimationPresets,\n  handleApiError \n} from '@/lib/api';\nimport { \n  TextInput, \n  AdvancedTextInput, \n  AnimationRequest, \n  MotionResponse, \n  AnimationResponse,\n  AnimationPresets \n} from '@/types/motion';\nimport { <PERSON>, Settings, Sparkles, User, Zap } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\ninterface MotionGeneratorProps {\n  onResponse: (response: MotionResponse | AnimationResponse) => void;\n  onLoading: (loading: boolean) => void;\n  onError: (error: string | null) => void;\n  selectedExample?: string;\n}\n\nexport default function MotionGenerator({ \n  onResponse, \n  onLoading, \n  onError, \n  selectedExample \n}: MotionGeneratorProps) {\n  const [text, setText] = useState('');\n  const [characterId, setCharacterId] = useState('default');\n  const [animatorLevel, setAnimatorLevel] = useState<'junior' | 'intermediate' | 'senior'>('intermediate');\n  const [generationType, setGenerationType] = useState<'basic' | 'advanced' | 'professional'>('professional');\n  const [useLanggraph, setUseLanggraph] = useState(true);\n  const [presets, setPresets] = useState<AnimationPresets | null>(null);\n  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);\n\n  // Update text when example is selected\n  useEffect(() => {\n    if (selectedExample) {\n      setText(selectedExample);\n    }\n  }, [selectedExample]);\n\n  // Load animation presets\n  useEffect(() => {\n    const loadPresets = async () => {\n      try {\n        const presetsData = await getAnimationPresets();\n        setPresets(presetsData);\n      } catch (error) {\n        console.error('Failed to load presets:', error);\n      }\n    };\n    loadPresets();\n  }, []);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!text.trim()) {\n      toast.error('Please enter an animation description');\n      return;\n    }\n\n    onLoading(true);\n    onError(null);\n\n    try {\n      let response: MotionResponse | AnimationResponse;\n\n      switch (generationType) {\n        case 'basic':\n          const basicInput: TextInput = {\n            text: text.trim(),\n            character_id: characterId\n          };\n          response = await generateMotion(basicInput);\n          break;\n\n        case 'advanced':\n          const advancedInput: AdvancedTextInput = {\n            text: text.trim(),\n            character_id: characterId,\n            use_langgraph: useLanggraph\n          };\n          response = await generateMotionAdvanced(advancedInput);\n          break;\n\n        case 'professional':\n          const professionalInput: AnimationRequest = {\n            text: text.trim(),\n            character_id: characterId,\n            animator_level: animatorLevel,\n            quality_target: 'game_ready',\n            frame_rate: 30,\n            export_format: 'fbx',\n            reference_animations: []\n          };\n          response = await generateProfessionalAnimation(professionalInput);\n          break;\n\n        default:\n          throw new Error('Invalid generation type');\n      }\n\n      if (response.success) {\n        onResponse(response);\n        toast.success('Animation generated successfully!');\n      } else {\n        const errorMsg = response.error_message || 'Animation generation failed';\n        onError(errorMsg);\n        toast.error(errorMsg);\n      }\n    } catch (error) {\n      const errorMsg = handleApiError(error);\n      onError(errorMsg);\n      toast.error(errorMsg);\n    } finally {\n      onLoading(false);\n    }\n  };\n\n  const insertPreset = (preset: string) => {\n    const currentText = text.trim();\n    const newText = currentText ? `${currentText}, ${preset}` : preset;\n    setText(newText);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center gap-3 mb-6\">\n        <Sparkles className=\"w-6 h-6 text-blue-600\" />\n        <h2 className=\"text-xl font-bold\">Motion Generator</h2>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Generation Type Selection */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n            Generation Type\n          </label>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n            <button\n              type=\"button\"\n              onClick={() => setGenerationType('basic')}\n              className={`p-4 rounded-lg border-2 transition-all ${\n                generationType === 'basic'\n                  ? 'border-blue-500 bg-blue-50 text-blue-700'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <Play className=\"w-5 h-5 mx-auto mb-2\" />\n              <div className=\"text-sm font-medium\">Basic</div>\n              <div className=\"text-xs text-gray-500\">Simple motion generation</div>\n            </button>\n            \n            <button\n              type=\"button\"\n              onClick={() => setGenerationType('advanced')}\n              className={`p-4 rounded-lg border-2 transition-all ${\n                generationType === 'advanced'\n                  ? 'border-blue-500 bg-blue-50 text-blue-700'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <Zap className=\"w-5 h-5 mx-auto mb-2\" />\n              <div className=\"text-sm font-medium\">Advanced</div>\n              <div className=\"text-xs text-gray-500\">LangGraph pipeline</div>\n            </button>\n            \n            <button\n              type=\"button\"\n              onClick={() => setGenerationType('professional')}\n              className={`p-4 rounded-lg border-2 transition-all ${\n                generationType === 'professional'\n                  ? 'border-blue-500 bg-blue-50 text-blue-700'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <User className=\"w-5 h-5 mx-auto mb-2\" />\n              <div className=\"text-sm font-medium\">Professional</div>\n              <div className=\"text-xs text-gray-500\">Full animation pipeline</div>\n            </button>\n          </div>\n        </div>\n\n        {/* Text Input */}\n        <div>\n          <label htmlFor=\"text\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Animation Description\n          </label>\n          <textarea\n            id=\"text\"\n            value={text}\n            onChange={(e) => setText(e.target.value)}\n            placeholder=\"Describe the animation you want to generate... (e.g., 'walk forward three steps, then jump and land')\"\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n            rows={4}\n            required\n          />\n        </div>\n\n        {/* Quick Presets */}\n        {presets && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Quick Actions (click to add)\n            </label>\n            <div className=\"space-y-2\">\n              {Object.entries(presets).map(([category, actions]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-gray-500 uppercase tracking-wide mb-1\">\n                    {category}\n                  </div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {actions.slice(0, 6).map((action) => (\n                      <button\n                        key={action}\n                        type=\"button\"\n                        onClick={() => insertPreset(action)}\n                        className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors\"\n                      >\n                        {action}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Advanced Options Toggle */}\n        <button\n          type=\"button\"\n          onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}\n          className=\"flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700\"\n        >\n          <Settings className=\"w-4 h-4\" />\n          {showAdvancedOptions ? 'Hide' : 'Show'} Advanced Options\n        </button>\n\n        {/* Advanced Options */}\n        {showAdvancedOptions && (\n          <div className=\"space-y-4 p-4 bg-gray-50 rounded-lg\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"characterId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Character ID\n                </label>\n                <input\n                  id=\"characterId\"\n                  type=\"text\"\n                  value={characterId}\n                  onChange={(e) => setCharacterId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              {generationType === 'professional' && (\n                <div>\n                  <label htmlFor=\"animatorLevel\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Animator Level\n                  </label>\n                  <select\n                    id=\"animatorLevel\"\n                    value={animatorLevel}\n                    onChange={(e) => setAnimatorLevel(e.target.value as 'junior' | 'intermediate' | 'senior')}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"junior\">Junior</option>\n                    <option value=\"intermediate\">Intermediate</option>\n                    <option value=\"senior\">Senior</option>\n                  </select>\n                </div>\n              )}\n            </div>\n\n            {generationType === 'advanced' && (\n              <div className=\"flex items-center\">\n                <input\n                  id=\"useLanggraph\"\n                  type=\"checkbox\"\n                  checked={useLanggraph}\n                  onChange={(e) => setUseLanggraph(e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"useLanggraph\" className=\"ml-2 block text-sm text-gray-700\">\n                  Use LangGraph Pipeline\n                </label>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium\"\n        >\n          Generate Animation\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAeA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAnBA;;;;;AA4Be,SAAS,gBAAgB,EACtC,UAAU,EACV,SAAS,EACT,OAAO,EACP,eAAe,EACM;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAwC;IACzF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAyC;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA2B;IAChE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,uCAAuC;IACvC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,iBAAiB;gBACnB,QAAQ;YACV;QACF;oCAAG;QAAC;KAAgB;IAEpB,yBAAyB;IACzB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;yDAAc;oBAClB,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD;wBAC5C,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;;YACA;QACF;oCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,ySAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,UAAU;QACV,QAAQ;QAER,IAAI;YACF,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,MAAM,aAAwB;wBAC5B,MAAM,KAAK,IAAI;wBACf,cAAc;oBAChB;oBACA,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;oBAChC;gBAEF,KAAK;oBACH,MAAM,gBAAmC;wBACvC,MAAM,KAAK,IAAI;wBACf,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,yBAAsB,AAAD,EAAE;oBACxC;gBAEF,KAAK;oBACH,MAAM,oBAAsC;wBAC1C,MAAM,KAAK,IAAI;wBACf,cAAc;wBACd,gBAAgB;wBAChB,gBAAgB;wBAChB,YAAY;wBACZ,eAAe;wBACf,sBAAsB,EAAE;oBAC1B;oBACA,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,gCAA6B,AAAD,EAAE;oBAC/C;gBAEF;oBACE,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,WAAW;gBACX,ySAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,WAAW,SAAS,aAAa,IAAI;gBAC3C,QAAQ;gBACR,ySAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;YAChC,QAAQ;YACR,ySAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc,KAAK,IAAI;QAC7B,MAAM,UAAU,cAAc,GAAG,YAAY,EAAE,EAAE,QAAQ,GAAG;QAC5D,QAAQ;IACV;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,iSAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,4TAAC;wBAAG,WAAU;kCAAoB;;;;;;;;;;;;0BAGpC,4TAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,4TAAC;;0CACC,4TAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,UACf,6CACA,yCACJ;;0DAEF,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4TAAC;gDAAI,WAAU;0DAAsB;;;;;;0DACrC,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAGzC,4TAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,aACf,6CACA,yCACJ;;0DAEF,4TAAC,uRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,4TAAC;gDAAI,WAAU;0DAAsB;;;;;;0DACrC,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAGzC,4TAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,iBACf,6CACA,yCACJ;;0DAEF,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4TAAC;gDAAI,WAAU;0DAAsB;;;;;;0DACrC,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,4TAAC;;0CACC,4TAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,4TAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACvC,aAAY;gCACZ,WAAU;gCACV,MAAM;gCACN,QAAQ;;;;;;;;;;;;oBAKX,yBACC,4TAAC;;0CACC,4TAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4TAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ,iBAC/C,4TAAC;;0DACC,4TAAC;gDAAI,WAAU;0DACZ;;;;;;0DAEH,4TAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBACxB,4TAAC;wDAEC,MAAK;wDACL,SAAS,IAAM,aAAa;wDAC5B,WAAU;kEAET;uDALI;;;;;;;;;;;uCAPH;;;;;;;;;;;;;;;;kCAuBlB,4TAAC;wBACC,MAAK;wBACL,SAAS,IAAM,uBAAuB,CAAC;wBACvC,WAAU;;0CAEV,4TAAC,iSAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,sBAAsB,SAAS;4BAAO;;;;;;;oBAIxC,qCACC,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;;0DACC,4TAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA+C;;;;;;0DAGtF,4TAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;oCAIb,mBAAmB,gCAClB,4TAAC;;0DACC,4TAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAA+C;;;;;;0DAGxF,4TAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,4TAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,4TAAC;wDAAO,OAAM;kEAAe;;;;;;kEAC7B,4TAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;4BAM9B,mBAAmB,4BAClB,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCACC,IAAG;wCACH,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;wCACjD,WAAU;;;;;;kDAEZ,4TAAC;wCAAM,SAAQ;wCAAe,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;kCASnF,4TAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GA3RwB;KAAA", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/llm/motion-agent/frontend/src/components/AnimationPreview.tsx"], "sourcesContent": ["'use client';\n\nimport { MotionResponse, AnimationResponse } from '@/types/motion';\nimport { CheckCircle, AlertCircle, Clock, Download, FileText, Zap } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\ninterface AnimationPreviewProps {\n  response: MotionResponse | AnimationResponse | null;\n  loading: boolean;\n  error: string | null;\n}\n\nexport default function AnimationPreview({ response, loading, error }: AnimationPreviewProps) {\n  const handleDownload = async (filePath: string) => {\n    try {\n      // Extract filename from the full path\n      const filename = filePath.split('/').pop() || 'animation.fbx';\n\n      // Create download URL using the same base URL as the API\n      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001';\n      const downloadUrl = `${API_BASE_URL}/animation/download/${filename}`;\n\n      toast.loading('Downloading FBX file...', { id: 'download' });\n\n      // Fetch the file\n      const response = await fetch(downloadUrl);\n\n      if (!response.ok) {\n        throw new Error(`Download failed: ${response.statusText}`);\n      }\n\n      // Create blob and download\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n\n      toast.success('FBX file downloaded successfully!', { id: 'download' });\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error(`Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: 'download' });\n    }\n  };\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"flex items-center gap-3 mb-4\">\n          <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n          <h3 className=\"text-lg font-semibold\">Generating Animation...</h3>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"animate-pulse h-4 bg-gray-200 rounded w-3/4\"></div>\n          <div className=\"animate-pulse h-4 bg-gray-200 rounded w-1/2\"></div>\n          <div className=\"animate-pulse h-4 bg-gray-200 rounded w-2/3\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"flex items-center gap-3 mb-4\">\n          <AlertCircle className=\"w-6 h-6 text-red-500\" />\n          <h3 className=\"text-lg font-semibold text-red-700\">Error</h3>\n        </div>\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-700\">{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!response) {\n    return (\n      <div className=\"bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 p-8 text-center\">\n        <FileText className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-600 mb-2\">No Animation Generated</h3>\n        <p className=\"text-gray-500\">Enter a description and generate an animation to see results here.</p>\n      </div>\n    );\n  }\n\n  const isAdvancedResponse = 'animation_sequence' in response;\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center gap-3 mb-6\">\n        <CheckCircle className=\"w-6 h-6 text-green-500\" />\n        <h3 className=\"text-lg font-semibold\">Animation Generated Successfully</h3>\n      </div>\n\n      {/* Success Message */}\n      <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\">\n        <p className=\"text-green-700 font-medium\">{response.message}</p>\n      </div>\n\n      {/* Basic Motion Response */}\n      {!isAdvancedResponse && response.action_sequence && (\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"bg-blue-50 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Zap className=\"w-4 h-4 text-blue-600\" />\n                <h4 className=\"font-medium text-blue-900\">Actions</h4>\n              </div>\n              <div className=\"flex flex-wrap gap-2\">\n                {response.action_sequence.actions.map((action, index) => (\n                  <span\n                    key={index}\n                    className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm\"\n                  >\n                    {action}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"bg-purple-50 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Clock className=\"w-4 h-4 text-purple-600\" />\n                <h4 className=\"font-medium text-purple-900\">Duration</h4>\n              </div>\n              <p className=\"text-2xl font-bold text-purple-800\">\n                {response.action_sequence.duration}s\n              </p>\n              {response.action_sequence.complexity && (\n                <p className=\"text-sm text-purple-600 mt-1\">\n                  Complexity: {response.action_sequence.complexity}\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Advanced Animation Response */}\n      {isAdvancedResponse && (\n        <div className=\"space-y-6\">\n          {/* Animation Sequence Info */}\n          {response.animation_sequence && (\n            <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6\">\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Animation Sequence Details</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">Duration</p>\n                  <p className=\"text-lg font-semibold\">{response.animation_sequence.total_duration}s</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Frame Rate</p>\n                  <p className=\"text-lg font-semibold\">{response.animation_sequence.frame_rate} FPS</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Total Frames</p>\n                  <p className=\"text-lg font-semibold\">{response.animation_sequence.total_frames}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Processed Actions */}\n          {response.processed_actions && response.processed_actions.length > 0 && (\n            <div className=\"bg-green-50 rounded-lg p-4\">\n              <h4 className=\"font-medium text-green-900 mb-3\">Processed Actions</h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {response.processed_actions.map((action, index) => (\n                  <span\n                    key={index}\n                    className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm\"\n                  >\n                    {action}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* FBX File Download */}\n          {response.fbx_file_path && (\n            <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Download className=\"w-5 h-5 text-orange-600\" />\n                  <div>\n                    <h4 className=\"font-medium text-orange-900\">FBX File Ready</h4>\n                    <p className=\"text-sm text-orange-700\">{response.fbx_file_path}</p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => handleDownload(response.fbx_file_path)}\n                  className=\"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\"\n                >\n                  Download\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Quality Report */}\n          {response.quality_report && Object.keys(response.quality_report).length > 0 && (\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h4 className=\"font-medium text-gray-900 mb-3\">Quality Report</h4>\n              <pre className=\"text-sm text-gray-700 bg-white p-3 rounded border overflow-x-auto\">\n                {JSON.stringify(response.quality_report, null, 2)}\n              </pre>\n            </div>\n          )}\n\n          {/* Processing Time */}\n          {response.processing_time && (\n            <div className=\"text-center text-sm text-gray-500\">\n              Processing completed in {response.processing_time.toFixed(2)} seconds\n            </div>\n          )}\n\n          {/* Warnings */}\n          {response.warnings && response.warnings.length > 0 && (\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-yellow-900 mb-2\">Warnings</h4>\n              <ul className=\"list-disc list-inside space-y-1\">\n                {response.warnings.map((warning, index) => (\n                  <li key={index} className=\"text-yellow-800 text-sm\">{warning}</li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAmB2B;;AAhB3B;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAYe,SAAS,iBAAiB,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAyB;IAC1F,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,sCAAsC;YACtC,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;YAE9C,yDAAyD;YACzD,MAAM,eAAe,+RAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;YACxD,MAAM,cAAc,GAAG,aAAa,oBAAoB,EAAE,UAAU;YAEpE,ySAAA,CAAA,UAAK,CAAC,OAAO,CAAC,2BAA2B;gBAAE,IAAI;YAAW;YAE1D,iBAAiB;YACjB,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS,UAAU,EAAE;YAC3D;YAEA,2BAA2B;YAC3B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,ySAAA,CAAA,UAAK,CAAC,OAAO,CAAC,qCAAqC;gBAAE,IAAI;YAAW;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,ySAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB,EAAE;gBAAE,IAAI;YAAW;QAC/G;IACF;IACA,IAAI,SAAS;QACX,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;;;;;sCACf,4TAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;;;;;sCACf,4TAAC;4BAAI,WAAU;;;;;;sCACf,4TAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,OAAO;QACT,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,4TAAC;4BAAG,WAAU;sCAAqC;;;;;;;;;;;;8BAErD,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC,qSAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,4TAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,4TAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,qBAAqB,wBAAwB;IAEnD,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,2SAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,4TAAC;wBAAG,WAAU;kCAAwB;;;;;;;;;;;;0BAIxC,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAE,WAAU;8BAA8B,SAAS,OAAO;;;;;;;;;;;YAI5D,CAAC,sBAAsB,SAAS,eAAe,kBAC9C,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,uRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,4TAAC;4CAAG,WAAU;sDAA4B;;;;;;;;;;;;8CAE5C,4TAAC;oCAAI,WAAU;8CACZ,SAAS,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7C,4TAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;;;;;;;;;;;;sCASb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,4TAAC;4CAAG,WAAU;sDAA8B;;;;;;;;;;;;8CAE9C,4TAAC;oCAAE,WAAU;;wCACV,SAAS,eAAe,CAAC,QAAQ;wCAAC;;;;;;;gCAEpC,SAAS,eAAe,CAAC,UAAU,kBAClC,4TAAC;oCAAE,WAAU;;wCAA+B;wCAC7B,SAAS,eAAe,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;YAS3D,oCACC,4TAAC;gBAAI,WAAU;;oBAEZ,SAAS,kBAAkB,kBAC1B,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;;0DACC,4TAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,4TAAC;gDAAE,WAAU;;oDAAyB,SAAS,kBAAkB,CAAC,cAAc;oDAAC;;;;;;;;;;;;;kDAEnF,4TAAC;;0DACC,4TAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,4TAAC;gDAAE,WAAU;;oDAAyB,SAAS,kBAAkB,CAAC,UAAU;oDAAC;;;;;;;;;;;;;kDAE/E,4TAAC;;0DACC,4TAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,4TAAC;gDAAE,WAAU;0DAAyB,SAAS,kBAAkB,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;oBAOrF,SAAS,iBAAiB,IAAI,SAAS,iBAAiB,CAAC,MAAM,GAAG,mBACjE,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,4TAAC;gCAAI,WAAU;0CACZ,SAAS,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACvC,4TAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;oBAWd,SAAS,aAAa,kBACrB,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,4TAAC;oDAAE,WAAU;8DAA2B,SAAS,aAAa;;;;;;;;;;;;;;;;;;8CAGlE,4TAAC;oCACC,SAAS,IAAM,eAAe,SAAS,aAAa;oCACpD,WAAU;8CACX;;;;;;;;;;;;;;;;;oBAQN,SAAS,cAAc,IAAI,OAAO,IAAI,CAAC,SAAS,cAAc,EAAE,MAAM,GAAG,mBACxE,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,4TAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,SAAS,cAAc,EAAE,MAAM;;;;;;;;;;;;oBAMpD,SAAS,eAAe,kBACvB,4TAAC;wBAAI,WAAU;;4BAAoC;4BACxB,SAAS,eAAe,CAAC,OAAO,CAAC;4BAAG;;;;;;;oBAKhE,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,4TAAC;gCAAG,WAAU;0CACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,4TAAC;wCAAe,WAAU;kDAA2B;uCAA5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;KA/NwB", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/llm/motion-agent/frontend/src/components/ExampleRequests.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getExampleRequests } from '@/lib/api';\nimport { ExampleRequest } from '@/types/motion';\nimport { Lightbulb, Copy, CheckCircle } from 'lucide-react';\n\ninterface ExampleRequestsProps {\n  onSelectExample: (text: string) => void;\n}\n\nexport default function ExampleRequests({ onSelectExample }: ExampleRequestsProps) {\n  const [examples, setExamples] = useState<{\n    simple_examples: ExampleRequest[];\n    intermediate_examples: ExampleRequest[];\n    advanced_examples: ExampleRequest[];\n  } | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [copiedIndex, setCopiedIndex] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchExamples = async () => {\n      try {\n        const data = await getExampleRequests();\n        setExamples(data);\n      } catch (error) {\n        console.error('Failed to fetch examples:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchExamples();\n  }, []);\n\n  const handleCopyExample = async (text: string, index: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedIndex(index);\n      setTimeout(() => setCopiedIndex(null), 2000);\n    } catch (error) {\n      console.error('Failed to copy text:', error);\n    }\n  };\n\n  const handleSelectExample = (text: string) => {\n    onSelectExample(text);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"flex items-center gap-2 mb-4\">\n          <Lightbulb className=\"w-5 h-5 text-yellow-500\" />\n          <h3 className=\"text-lg font-semibold\">Example Requests</h3>\n        </div>\n        <div className=\"animate-pulse space-y-3\">\n          {[1, 2, 3].map((i) => (\n            <div key={i} className=\"h-16 bg-gray-200 rounded\"></div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (!examples) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"flex items-center gap-2 mb-4\">\n          <Lightbulb className=\"w-5 h-5 text-yellow-500\" />\n          <h3 className=\"text-lg font-semibold\">Example Requests</h3>\n        </div>\n        <p className=\"text-gray-500\">Failed to load examples</p>\n      </div>\n    );\n  }\n\n  const renderExampleGroup = (title: string, exampleList: ExampleRequest[], prefix: string) => (\n    <div className=\"mb-6\">\n      <h4 className=\"text-md font-medium text-gray-700 mb-3\">{title}</h4>\n      <div className=\"space-y-2\">\n        {exampleList.map((example, index) => {\n          const uniqueId = `${prefix}-${index}`;\n          return (\n            <div\n              key={uniqueId}\n              className=\"border border-gray-200 rounded-lg p-3 hover:border-blue-300 transition-colors cursor-pointer group\"\n              onClick={() => handleSelectExample(example.text)}\n            >\n              <div className=\"flex justify-between items-start\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-900 group-hover:text-blue-600\">\n                    {example.text}\n                  </p>\n                  <p className=\"text-xs text-gray-500 mt-1\">{example.description}</p>\n                  <span className=\"inline-block mt-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                    {example.animator_level}\n                  </span>\n                </div>\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleCopyExample(example.text, uniqueId);\n                  }}\n                  className=\"ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors\"\n                  title=\"Copy to clipboard\"\n                >\n                  {copiedIndex === uniqueId ? (\n                    <CheckCircle className=\"w-4 h-4 text-green-500\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center gap-2 mb-6\">\n        <Lightbulb className=\"w-5 h-5 text-yellow-500\" />\n        <h3 className=\"text-lg font-semibold\">Example Requests</h3>\n      </div>\n\n      {renderExampleGroup('Simple Examples', examples.simple_examples, 'simple')}\n      {renderExampleGroup('Intermediate Examples', examples.intermediate_examples, 'intermediate')}\n      {renderExampleGroup('Advanced Examples', examples.advanced_examples, 'advanced')}\n\n      <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n        <p className=\"text-sm text-blue-700\">\n          💡 Click on any example to use it as your input, or copy it to modify as needed.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;;;AALA;;;;AAWe,SAAS,gBAAgB,EAAE,eAAe,EAAwB;;IAC/E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAI7B;IACV,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;2DAAgB;oBACpB,IAAI;wBACF,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD;wBACpC,YAAY;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6BAA6B;oBAC7C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;oCAAG,EAAE;IAEL,MAAM,oBAAoB,OAAO,MAAc;QAC7C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,mSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,4TAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,4TAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,4TAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;IAKpB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,mSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,4TAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,4TAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,qBAAqB,CAAC,OAAe,aAA+B,uBACxE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAG,WAAU;8BAA0C;;;;;;8BACxD,4TAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,SAAS;wBACzB,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,OAAO;wBACrC,qBACE,4TAAC;4BAEC,WAAU;4BACV,SAAS,IAAM,oBAAoB,QAAQ,IAAI;sCAE/C,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAE,WAAU;0DACV,QAAQ,IAAI;;;;;;0DAEf,4TAAC;gDAAE,WAAU;0DAA8B,QAAQ,WAAW;;;;;;0DAC9D,4TAAC;gDAAK,WAAU;0DACb,QAAQ,cAAc;;;;;;;;;;;;kDAG3B,4TAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,kBAAkB,QAAQ,IAAI,EAAE;wCAClC;wCACA,WAAU;wCACV,OAAM;kDAEL,gBAAgB,yBACf,4TAAC,2SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BAzBjB;;;;;oBA+BX;;;;;;;;;;;;IAKN,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,mSAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,4TAAC;wBAAG,WAAU;kCAAwB;;;;;;;;;;;;YAGvC,mBAAmB,mBAAmB,SAAS,eAAe,EAAE;YAChE,mBAAmB,yBAAyB,SAAS,qBAAqB,EAAE;YAC5E,mBAAmB,qBAAqB,SAAS,iBAAiB,EAAE;0BAErE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAM7C;GAhIwB;KAAA", "debugId": null}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/llm/motion-agent/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport MotionGenerator from '@/components/MotionGenerator';\nimport AnimationPreview from '@/components/AnimationPreview';\nimport ExampleRequests from '@/components/ExampleRequests';\nimport { MotionResponse, AnimationResponse, HealthStatus } from '@/types/motion';\nimport { checkHealth } from '@/lib/api';\nimport { Activity, AlertCircle, CheckCircle } from 'lucide-react';\n\nexport default function Home() {\n  const [response, setResponse] = useState<MotionResponse | AnimationResponse | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedExample, setSelectedExample] = useState<string>('');\n  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');\n  const [healthData, setHealthData] = useState<HealthStatus | null>(null);\n\n  // Check backend health on component mount\n  useEffect(() => {\n    const checkBackendHealth = async () => {\n      try {\n        const health = await checkHealth();\n        setHealthData(health);\n        setBackendStatus('online');\n      } catch (error) {\n        console.error('Backend health check failed:', error);\n        setBackendStatus('offline');\n      }\n    };\n\n    checkBackendHealth();\n    // Check health every 30 seconds\n    const interval = setInterval(checkBackendHealth, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleExampleSelect = (text: string) => {\n    setSelectedExample(text);\n    // Clear previous results when selecting new example\n    setResponse(null);\n    setError(null);\n  };\n\n  const StatusIndicator = () => (\n    <div className=\"flex items-center gap-2 text-sm\">\n      {backendStatus === 'checking' && (\n        <>\n          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n          <span className=\"text-gray-600\">Checking backend...</span>\n        </>\n      )}\n      {backendStatus === 'online' && (\n        <>\n          <CheckCircle className=\"w-4 h-4 text-green-500\" />\n          <span className=\"text-green-600\">Backend Online</span>\n          {healthData && (\n            <span className=\"text-gray-500\">v{healthData.version}</span>\n          )}\n        </>\n      )}\n      {backendStatus === 'offline' && (\n        <>\n          <AlertCircle className=\"w-4 h-4 text-red-500\" />\n          <span className=\"text-red-600\">Backend Offline</span>\n        </>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <Activity className=\"w-8 h-8 text-blue-600\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">Motion Agent</h1>\n                <p className=\"text-sm text-gray-600\">Professional 3D Animation Generator</p>\n              </div>\n            </div>\n            <StatusIndicator />\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Left Column - Input and Examples */}\n          <div className=\"space-y-8\">\n            <MotionGenerator\n              onResponse={setResponse}\n              onLoading={setLoading}\n              onError={setError}\n              selectedExample={selectedExample}\n            />\n\n            <ExampleRequests onSelectExample={handleExampleSelect} />\n          </div>\n\n          {/* Right Column - Preview */}\n          <div>\n            <AnimationPreview\n              response={response}\n              loading={loading}\n              error={error}\n            />\n          </div>\n        </div>\n\n        {/* Backend Status Details */}\n        {healthData && backendStatus === 'online' && (\n          <div className=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n            <h3 className=\"text-lg font-semibold mb-4\">Backend Status</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                <div className=\"text-sm text-gray-600\">NLU Pipeline</div>\n                <div className=\"font-semibold text-green-700\">\n                  {healthData.nlu_pipeline || 'Ready'}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n                <div className=\"text-sm text-gray-600\">LangGraph</div>\n                <div className=\"font-semibold text-blue-700\">\n                  {healthData.langgraph_pipeline || 'Ready'}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-purple-50 rounded-lg\">\n                <div className=\"text-sm text-gray-600\">Professional Animator</div>\n                <div className=\"font-semibold text-purple-700\">\n                  {healthData.professional_animator || 'Ready'}\n                </div>\n              </div>\n              <div className=\"text-center p-3 bg-orange-50 rounded-lg\">\n                <div className=\"text-sm text-gray-600\">Version</div>\n                <div className=\"font-semibold text-orange-700\">{healthData.version}</div>\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>Motion Agent - Professional 3D Animation Generator</p>\n            <p className=\"text-sm mt-2\">\n              Powered by MotionGPT + Motion Agent + MoDi + Blender + Python\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA6C;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAqC;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAuB;IAElE,0CAA0C;IAC1C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;qDAAqB;oBACzB,IAAI;wBACF,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;wBAC/B,cAAc;wBACd,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,iBAAiB;oBACnB;gBACF;;YAEA;YACA,gCAAgC;YAChC,MAAM,WAAW,YAAY,oBAAoB;YACjD;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,oDAAoD;QACpD,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,kBAAkB,kBACtB,4TAAC;YAAI,WAAU;;gBACZ,kBAAkB,4BACjB;;sCACE,4TAAC;4BAAI,WAAU;;;;;;sCACf,4TAAC;4BAAK,WAAU;sCAAgB;;;;;;;;gBAGnC,kBAAkB,0BACjB;;sCACE,4TAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,4TAAC;4BAAK,WAAU;sCAAiB;;;;;;wBAChC,4BACC,4TAAC;4BAAK,WAAU;;gCAAgB;gCAAE,WAAW,OAAO;;;;;;;;;gBAIzD,kBAAkB,2BACjB;;sCACE,4TAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,4TAAC;4BAAK,WAAU;sCAAe;;;;;;;;;;;;;;IAMvC,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,ySAAA,CAAA,UAAO;gBAAC,UAAS;;;;;;0BAGlB,4TAAC;gBAAO,WAAU;0BAChB,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,iSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,4TAAC;;0DACC,4TAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,4TAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,4TAAC;;;;;;;;;;;;;;;;;;;;;0BAMP,4TAAC;gBAAK,WAAU;;kCACd,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,wIAAA,CAAA,UAAe;wCACd,YAAY;wCACZ,WAAW;wCACX,SAAS;wCACT,iBAAiB;;;;;;kDAGnB,4TAAC,wIAAA,CAAA,UAAe;wCAAC,iBAAiB;;;;;;;;;;;;0CAIpC,4TAAC;0CACC,cAAA,4TAAC,yIAAA,CAAA,UAAgB;oCACf,UAAU;oCACV,SAAS;oCACT,OAAO;;;;;;;;;;;;;;;;;oBAMZ,cAAc,kBAAkB,0BAC/B,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,4TAAC;gDAAI,WAAU;0DACZ,WAAW,YAAY,IAAI;;;;;;;;;;;;kDAGhC,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,4TAAC;gDAAI,WAAU;0DACZ,WAAW,kBAAkB,IAAI;;;;;;;;;;;;kDAGtC,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,4TAAC;gDAAI,WAAU;0DACZ,WAAW,qBAAqB,IAAI;;;;;;;;;;;;kDAGzC,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,4TAAC;gDAAI,WAAU;0DAAiC,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5E,4TAAC;gBAAO,WAAU;0BAChB,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;0CAAE;;;;;;0CACH,4TAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;GAtJwB;KAAA", "debugId": null}}]}