#!/usr/bin/env python3
"""
简单的动画管道测试
Simple Animation Pipeline Test
"""

import asyncio
import json
import os
import sys
import time

# 添加backend到路径
sys.path.append('backend')

from animation.models import AnimationRequest
from animation.professional_pipeline import ProfessionalAnimationPipeline


async def test_simple_animation_generation():
    """测试简单的动画生成"""
    print("🎬 测试简单动画生成管道")
    print("=" * 50)
    
    try:
        # 创建管道
        print("🔧 初始化动画管道...")
        pipeline = ProfessionalAnimationPipeline()
        print("✅ 动画管道初始化成功")
        
        # 创建简单的测试请求
        test_cases = [
            {
                "text": "向前走三步",
                "character_id": "test_character_1",
                "description": "简单移动测试"
            },
            {
                "text": "挥手打招呼",
                "character_id": "test_character_2", 
                "description": "手势动作测试"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🎯 测试案例 {i}: {test_case['description']}")
            print(f"📝 输入: {test_case['text']}")
            
            # 创建请求
            request = AnimationRequest(
                text=test_case['text'],
                character_id=test_case['character_id'],
                animator_level="junior",
                quality_target="game_ready",
                frame_rate=30,
                export_format="fbx"
            )
            
            try:
                # 处理请求
                print("⚙️  处理动画请求...")
                response = await pipeline.process_animation_request(request)
                
                if response.success:
                    print("✅ 动画生成成功!")
                    print(f"📊 动作数量: {len(response.processed_actions)}")
                    print(f"🎬 动作列表: {', '.join(response.processed_actions)}")
                    if response.animation_sequence:
                        print(f"⏱️  总时长: {response.animation_sequence.total_duration:.2f}秒")
                        print(f"🎞️  总帧数: {response.animation_sequence.total_frames}")
                    print(f"📁 FBX文件: {response.fbx_file_path}")
                    print(f"⚡ 处理时间: {response.processing_time:.2f}秒")
                    
                    # 检查FBX文件是否真的生成了
                    if response.fbx_file_path and os.path.exists(response.fbx_file_path):
                        file_size = os.path.getsize(response.fbx_file_path)
                        print(f"✅ FBX文件确认存在，大小: {file_size} bytes")
                    else:
                        print(f"❌ FBX文件未找到: {response.fbx_file_path}")
                        
                else:
                    print("❌ 动画生成失败")
                    print(f"🚫 错误信息: {response.error_message}")
                    
            except Exception as e:
                print(f"💥 测试失败: {e}")
                import traceback
                traceback.print_exc()
            
            print("-" * 40)
            
    except Exception as e:
        print(f"💥 管道初始化失败: {e}")
        import traceback
        traceback.print_exc()


def check_output_directory():
    """检查输出目录"""
    print("\n📁 检查输出目录")
    print("=" * 20)
    
    output_dir = "output/animations"
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        if files:
            print(f"✅ 找到 {len(files)} 个文件:")
            for file in files:
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   - {file} ({file_size} bytes)")
        else:
            print("📂 输出目录为空")
    else:
        print("❌ 输出目录不存在")


def main():
    """主测试函数"""
    print("🚀 启动简单动画管道测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import spacy
        import numpy
        print("✅ 基础依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖缺失: {e}")
        return
    
    # 检查Blender
    blender_path = "/Applications/Blender.app/Contents/MacOS/Blender"
    if os.path.exists(blender_path):
        print("✅ Blender可用")
    else:
        print("❌ Blender未找到")
        return
    
    # 运行测试
    try:
        # 检查现有文件
        check_output_directory()
        
        # 运行异步测试
        asyncio.run(test_simple_animation_generation())
        
        # 再次检查输出
        check_output_directory()
        
        print("\n🎉 测试完成！")
        print("=" * 20)
        
    except Exception as e:
        print(f"💥 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
