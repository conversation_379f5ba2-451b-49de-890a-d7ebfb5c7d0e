#!/usr/bin/env python3
"""
测试Blender FBX生成功能
Test Blender FBX Generation Functionality
"""

import json
import os
import subprocess
import sys
import time
from pathlib import Path


def create_test_animation_data():
    """创建测试动画数据"""
    return {
        "sequence": {
            "name": "test_animation",
            "total_duration": 3.0,
            "frame_rate": 30,
            "total_frames": 90
        },
        "actions": [
            {
                "name": "walk",
                "type": "locomotion",
                "start_frame": 1,
                "end_frame": 60,
                "keyframes": {},
                "parameters": {
                    "direction": "forward",
                    "angle": None,
                    "steps": 3,
                    "intensity": "normal",
                    "body_parts": ["full_body"]
                }
            },
            {
                "name": "wave",
                "type": "gesture",
                "start_frame": 61,
                "end_frame": 90,
                "keyframes": {},
                "parameters": {
                    "direction": None,
                    "angle": None,
                    "steps": None,
                    "intensity": "normal",
                    "body_parts": ["right_hand"]
                }
            }
        ],
        "character": {
            "id": "test_character",
            "rig_type": "humanoid"
        },
        "export_settings": {
            "format": "fbx",
            "quality": "game_ready",
            "frame_rate": 30
        }
    }


def test_blender_script():
    """测试Blender脚本"""
    print("🎬 测试Blender FBX生成功能")
    print("=" * 50)
    
    # 检查Blender是否存在
    blender_path = "/Applications/Blender.app/Contents/MacOS/Blender"
    if not os.path.exists(blender_path):
        print("❌ Blender未找到，请安装Blender")
        return False
    
    print(f"✅ 找到Blender: {blender_path}")
    
    # 创建必要的目录
    temp_dir = "temp/animation_data"
    output_dir = "output/animations"
    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建测试数据
    test_data = create_test_animation_data()
    temp_data_file = os.path.join(temp_dir, f"test_animation_data_{int(time.time())}.json")
    
    with open(temp_data_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试数据: {temp_data_file}")
    
    # 生成输出文件路径
    output_filename = f"test_animation_{int(time.time())}.fbx"
    output_path = os.path.join(output_dir, output_filename)
    
    # 构建Blender命令
    blender_script = "blender_scripts/generate_animation.py"
    cmd = [
        blender_path,
        "--background",
        "--python", blender_script,
        "--",
        "--input", temp_data_file,
        "--output", output_path,
        "--format", "fbx"
    ]
    
    print(f"🚀 执行Blender命令:")
    print(f"   {' '.join(cmd)}")
    
    try:
        # 执行Blender
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        print(f"\n📊 Blender执行结果:")
        print(f"   返回码: {result.returncode}")
        
        if result.stdout:
            print(f"   标准输出:")
            print(f"   {result.stdout}")
        
        if result.stderr:
            print(f"   错误输出:")
            print(f"   {result.stderr}")
        
        # 检查输出文件是否生成
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ FBX文件生成成功!")
            print(f"   文件路径: {output_path}")
            print(f"   文件大小: {file_size} bytes")
            
            # 清理临时文件
            os.remove(temp_data_file)
            print(f"🧹 清理临时文件: {temp_data_file}")
            
            return True
        else:
            print(f"❌ FBX文件未生成: {output_path}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Blender执行超时")
        return False
    except Exception as e:
        print(f"❌ Blender执行失败: {e}")
        return False


def test_blender_script_directly():
    """直接测试Blender脚本（不通过subprocess）"""
    print("\n🔧 直接测试Blender脚本")
    print("=" * 30)
    
    # 检查脚本文件是否存在
    script_path = "blender_scripts/generate_animation.py"
    if not os.path.exists(script_path):
        print(f"❌ Blender脚本未找到: {script_path}")
        return False
    
    print(f"✅ 找到Blender脚本: {script_path}")
    
    # 读取脚本内容检查
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 检查关键函数
    key_functions = [
        "ProfessionalBlenderAnimator",
        "generate_animation",
        "export_animation",
        "main"
    ]
    
    for func in key_functions:
        if func in content:
            print(f"✅ 找到关键函数: {func}")
        else:
            print(f"❌ 缺少关键函数: {func}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 启动Blender FBX生成测试")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 测试脚本存在性
    if not test_blender_script_directly():
        print("❌ Blender脚本测试失败")
        return
    
    # 测试完整的Blender FBX生成流程
    if test_blender_script():
        print("\n🎉 Blender FBX生成测试成功!")
        print("=" * 30)
        print("✅ 所有测试通过")
        print("✅ FBX文件生成正常")
        print("\n💡 下一步:")
        print("1. 检查生成的FBX文件质量")
        print("2. 在3D软件中验证动画")
        print("3. 集成到完整的动画管道中")
    else:
        print("\n❌ Blender FBX生成测试失败!")
        print("=" * 30)
        print("🔍 可能的问题:")
        print("1. Blender路径不正确")
        print("2. Blender脚本有错误")
        print("3. 权限问题")
        print("4. 依赖缺失")


if __name__ == "__main__":
    main()
