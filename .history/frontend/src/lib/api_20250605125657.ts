import axios from 'axios';
import { 
  TextInput, 
  AdvancedTextInput, 
  AnimationRequest, 
  MotionResponse, 
  AnimationResponse, 
  AnimationPresets, 
  ExampleRequest,
  HealthStatus 
} from '@/types/motion';

// API Base URL - Backend runs on port 9000
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout for animation generation
  headers: {
    'Content-Type': 'application/json',
  },
});

// API Functions

/**
 * Health check endpoint
 */
export const checkHealth = async (): Promise<HealthStatus> => {
  const response = await api.get('/health');
  return response.data;
};

/**
 * Basic motion generation
 */
export const generateMotion = async (input: TextInput): Promise<MotionResponse> => {
  const response = await api.post('/generate-motion', input);
  return response.data;
};

/**
 * Advanced motion generation with LangGraph
 */
export const generateMotionAdvanced = async (input: AdvancedTextInput): Promise<MotionResponse> => {
  const response = await api.post('/generate-motion-advanced', input);
  return response.data;
};

/**
 * Professional animation generation
 */
export const generateProfessionalAnimation = async (request: AnimationRequest): Promise<AnimationResponse> => {
  const response = await api.post('/animation/generate', request);
  return response.data;
};

/**
 * Get animation presets
 */
export const getAnimationPresets = async (): Promise<AnimationPresets> => {
  const response = await api.get('/animation/presets');
  return response.data;
};

/**
 * Get example requests
 */
export const getExampleRequests = async (): Promise<{
  simple_examples: ExampleRequest[];
  intermediate_examples: ExampleRequest[];
  advanced_examples: ExampleRequest[];
}> => {
  const response = await api.get('/animation/examples');
  return response.data;
};

/**
 * Animation system health check
 */
export const checkAnimationHealth = async () => {
  const response = await api.get('/animation/health');
  return response.data;
};

// Error handling wrapper
export const handleApiError = (error: any): string => {
  if (error.response) {
    // Server responded with error status
    return error.response.data?.detail || error.response.data?.message || 'Server error occurred';
  } else if (error.request) {
    // Request was made but no response received
    return 'Unable to connect to the server. Please check if the backend is running.';
  } else {
    // Something else happened
    return error.message || 'An unexpected error occurred';
  }
};
