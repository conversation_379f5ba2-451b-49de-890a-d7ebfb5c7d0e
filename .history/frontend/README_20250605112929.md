# Motion Agent Frontend

A modern React/Next.js frontend for the Motion Agent 3D animation generation system.

## Features

- **Natural Language Input**: Describe animations in plain language
- **Multiple Generation Types**: Basic, Advanced (LangGraph), and Professional modes
- **Real-time Preview**: See generated animation details instantly
- **Animation Presets**: Quick access to common animation actions
- **Example Library**: Pre-built examples for different skill levels
- **Responsive Design**: Works on desktop and mobile devices
- **Backend Integration**: Seamless connection to FastAPI backend

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- Backend server running on port 9000

### Installation

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev
```

The application will be available at `http://localhost:3001` (or next available port).

### Backend Connection

The frontend expects the backend API to be running on `http://localhost:9000`. You can configure this by setting the `NEXT_PUBLIC_API_URL` environment variable:

```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:9000
```

## Usage

1. **Select Generation Type**: Choose between Basic, Advanced, or Professional animation generation
2. **Enter Description**: Describe your desired animation in natural language
3. **Configure Options**: Set character ID, animator level, and other advanced options
4. **Generate**: Click "Generate Animation" to create your animation
5. **View Results**: See the generated animation sequence, actions, and download links

### Example Inputs

- "walk forward three steps"
- "后空翻然后向前走" (backflip then walk forward)
- "jump, spin around, and wave"
- "angry expression then attack"

## API Integration

The frontend integrates with the following backend endpoints:

- `GET /health` - Health check and system status
- `POST /generate-motion` - Basic motion generation
- `POST /generate-motion-advanced` - Advanced motion with LangGraph
- `POST /animation/generate` - Professional animation generation
- `GET /animation/presets` - Available animation presets
- `GET /animation/examples` - Example requests

## Development

### Project Structure

```
frontend/
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── page.tsx        # Main page component
│   │   ├── layout.tsx      # Root layout
│   │   └── globals.css     # Global styles
│   ├── components/         # React components
│   │   ├── MotionGenerator.tsx    # Main input form
│   │   ├── AnimationPreview.tsx   # Results display
│   │   └── ExampleRequests.tsx    # Example library
│   ├── lib/               # Utilities
│   │   └── api.ts         # API client functions
│   └── types/             # TypeScript definitions
│       └── motion.ts      # Motion-related types
├── package.json
└── README.md
```

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint

## Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Axios** - HTTP client
- **React Hot Toast** - Notifications
- **Lucide React** - Icons

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the Motion Agent system for professional 3D animation generation.
