"""
FastAPI服务入口
Motion Agent Backend API Server
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uvicorn
import sys
from loguru import logger

# Import animation router (disabled due to missing dependencies)
# try:
#     from backend.animation import animation_router
# except ImportError:
#     # Fallback for relative imports when running from backend directory
#     from animation import animation_router

# Simple models for basic functionality
class MotionRequest(BaseModel):
    text: str
    character_id: str = "default"
    context: Optional[Dict[str, Any]] = None

class MotionResponse(BaseModel):
    success: bool
    message: str
    action_sequence: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

# Configure loguru
logger.remove()  # Remove default handler
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/motion_agent.log",
    rotation="10 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"
)

app = FastAPI(
    title="Professional Motion Agent API",
    description="Professional Game Animator - Natural Language to 3D Animation API",
    version="2.0.0"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize NLU pipelines (disabled for basic functionality)
nlu_pipeline = None
langgraph_pipeline = None


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global nlu_pipeline, langgraph_pipeline
    logger.info("Starting Motion Agent API server...")

    # Initialize both pipelines (disabled for basic functionality)
    # nlu_pipeline = NLUPipeline()
    # langgraph_pipeline = LangGraphNLUPipeline()

    logger.success("Basic Motion Agent API initialized successfully")
    logger.info("Motion Agent API is ready to serve requests")


class TextInput(BaseModel):
    text: str
    character_id: str = "default"


class AdvancedTextInput(BaseModel):
    text: str
    character_id: str = "default"
    use_langgraph: bool = True
    context: Optional[Dict[str, Any]] = None


# 包含专业动画师路由
app.include_router(animation_router)


@app.get("/")
async def root():
    """Health check endpoint"""
    logger.info("Root endpoint accessed")
    return {
        "message": "Professional Motion Agent API is running",
        "version": "2.0.0",
        "features": [
            "Professional Game Animator",
            "Natural Language to Animation",
            "Junior & Intermediate Animator Functions",
            "Blender Integration",
            "FBX Export"
        ]
    }


@app.post("/generate-motion", response_model=MotionResponse)
async def generate_motion(input_data: TextInput):
    """
    Generate 3D animation from natural language description
    """
    logger.info(f"Received motion generation request: {input_data.text}")
    try:
        # Parse natural language input
        motion_request = MotionRequest(
            text=input_data.text,
            character_id=input_data.character_id
        )

        logger.debug(f"Processing motion request for character: {input_data.character_id}")

        # Simulate motion generation (basic functionality)
        motion_response = MotionResponse(
            success=True,
            message=f"Successfully processed motion request: '{input_data.text}'",
            action_sequence={"actions": ["walk", "wave"], "duration": 5.0}
        )

        logger.success(f"Successfully generated motion simulation")
        return motion_response

    except Exception as e:
        logger.exception(f"Error processing motion request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/generate-motion-advanced", response_model=MotionResponse)
async def generate_motion_advanced(input_data: AdvancedTextInput):
    """
    Generate 3D animation using advanced LangGraph pipeline
    """
    logger.info(f"Received advanced motion generation request: {input_data.text}")
    try:
        logger.debug(f"Using advanced pipeline for character: {input_data.character_id}")

        # Simulate advanced motion generation
        motion_response = MotionResponse(
            success=True,
            message=f"Successfully processed advanced motion request: '{input_data.text}'",
            action_sequence={"actions": ["backflip", "walk", "turn"], "duration": 8.0, "complexity": "advanced"}
        )

        logger.success(f"Advanced pipeline successfully generated motion simulation")
        return motion_response

    except Exception as e:
        logger.exception(f"Error processing advanced motion request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Detailed health check"""
    logger.debug("Health check endpoint accessed")
    return {
        "status": "healthy",
        "nlu_pipeline": "ready" if nlu_pipeline else "not initialized",
        "langgraph_pipeline": "ready" if langgraph_pipeline else "not initialized",
        "professional_animator": "ready",
        "version": "2.0.0",
        "features": {
            "langchain": True,
            "langgraph": True,
            "loguru_logging": True,
            "ruff_linting": True,
            "professional_animator": True,
            "junior_animator": True,
            "intermediate_animator": True,
            "blender_integration": True,
            "fbx_export": True
        }
    }


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=9000,
        reload=True
    )
